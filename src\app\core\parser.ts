import h from "@/app/helpers/all";

/**
 * Parser class for processing and resolving entity references, function calls, and complex data structures.
 *
 * This parser handles:
 * - Entity references in bracket notation: [EntityId.field] or [field] (for current entity)
 * - Function calls: [FunctionName(arg1, arg2)] (e.g., Date, RandomNumber utilities)
 * - Nested object and array parsing with recursive resolution
 * - Text replacement with entity values and function results
 *
 * @example
 * ```typescript
 * const parser = new Parser(functions);
 * const result = parser.parse("[User.name] has [Date(1, d)] appointment");
 * // Returns: "<PERSON> has 12/25/2023 appointment"
 * ```
 */
export default class Parser {
  /**
   * Creates a new Parser instance
   * @param functions - Object containing utility functions that can be called during parsing
   */
  constructor(
    private readonly functions: Record<
      string,
      (...args: any[]) => any
    > | null = null
  ) {}

  /**
   * Main parsing entry point that processes any value type
   * @param valueToParse - The value to parse (string, object, array, or primitive)
   * @returns The parsed value with all references and functions resolved
   */
  public parse(valueToParse: any): any {
    // Early return for falsy values to avoid unnecessary processing
    if (!valueToParse) {
      return null;
    }

    return this.parseValue(valueToParse);
  }

  /**
   * Routes parsing based on value type
   * @param valueToParse - The value to parse
   * @returns Parsed value based on its type
   */
  private parseValue(valueToParse: any): any {
    if (h.isString(valueToParse)) {
      return this.parseText(valueToParse);
    } else {
      return this.parseObject(valueToParse);
    }
  }

  /**
   * Parses text strings containing entity references and function calls
   * @param valueToParse - The text string to parse
   * @returns Parsed text with references and functions resolved
   */
  private parseText(valueToParse: string): any {
    // Skip parsing if:
    // - Value is falsy or not a string
    // - Starts with '@' (special marker to skip parsing)
    // - Doesn't contain '[' (no references to parse)
    if (
      !valueToParse ||
      !h.isString(valueToParse) ||
      valueToParse.startsWith("@") ||
      !valueToParse.includes("[")
    ) {
      return valueToParse;
    }

    // Normalize bracket notation: ][  becomes .
    // This allows chained references like [Entity][Field] to become [Entity.Field]
    const normalizedValue = valueToParse.replace(/\]\[/g, ".");

    // Try to parse as a function call first
    const functionValue = this.parseFunction(normalizedValue);
    if (functionValue !== undefined) {
      return h.replaceEscapedCharacters(String(functionValue));
    }

    // Find and replace all entity references in the text
    const references = this.findReferences(normalizedValue);
    const parsedText = references.reduce((result, reference, index) => {
      const referenceValue = this.getReferenceValue(reference);

      if (h.isString(referenceValue)) {
        // Replace the reference with its string value
        result = result.replace(references[index], referenceValue);
      } else {
        // If reference resolves to non-string, return that value directly
        // This handles cases where the entire text is just a reference
        result = referenceValue;
      }

      return result;
    }, normalizedValue);

    return parsedText;
  }

  /**
   * Parses function calls in bracket notation
   * @param valueToParse - String containing potential function call like [Date(1, d)]
   * @returns Result of function call or undefined if not a function
   */
  private parseFunction(valueToParse: string): any {
    // Extract arguments block: (arg1, arg2, ...)
    const argsBlock = valueToParse.match(/\(.*[\s\S]*?\)/)?.[0];

    // Parse arguments if present, otherwise empty array
    const args = argsBlock?.replace(/^\((.*)\)$/, "$1").split(",") ?? [];

    // Extract the function key by removing brackets and arguments
    const key = valueToParse
      .replace(/^\[/, "")
      .replace(/]$/, "")
      .replace(argsBlock ?? "", "")
      .trim();

    // Find matching function using fuzzy key matching
    const functionKey = h.getObjectSimilarKey(Cypress.sdt.primitives, key);

    if (functionKey && this.functions) {
      try {
        // Call the function with parsed arguments
        return this.functions[functionKey](...args);
      } catch (error) {
        console.warn(`Error calling function '${functionKey}':`, error);
        return undefined;
      }
    }

    return undefined;
  }

  /**
   * Parses objects and arrays recursively, applying parsing to all nested values
   * @param valueToParse - Object or array to parse
   * @returns Parsed object/array with all nested values processed
   */
  private parseObject(valueToParse: any): any {
    // Handle arrays by recursively parsing each item
    if (h.isArray(valueToParse)) {
      return valueToParse.map((item) => this.parse(item));
    }

    // Handle objects by parsing each property value
    const parsedObject: Record<string, any> = {};

    for (const [key, value] of Object.entries(valueToParse)) {
      // Preserve null values as-is
      if (value === null) {
        parsedObject[key] = null;
        continue;
      }

      // Parse based on value type
      if (typeof value === "object") {
        // Recursively parse nested objects and arrays
        parsedObject[key] = this.parse(value);
      } else if (typeof value === "string") {
        // Parse string values for references and primitives
        parsedObject[key] = this.parseText(value);
      } else {
        // Preserve primitive values (numbers, booleans, etc.) as-is
        parsedObject[key] = value;
      }
    }

    return parsedObject;
  }

  /**
   * Finds all entity references in bracket notation within a text string
   * @param text - Text to search for references
   * @returns Array of reference strings including brackets
   */
  private findReferences(text: string): string[] {
    const regex = /\[([^\]]+)\]/g;
    const references: string[] = [];
    let match: RegExpExecArray | null;

    // Find all matches and collect them
    while ((match = regex.exec(text)) !== null) {
      references.push(`[${match[1]}]`);
    }

    return references;
  }

  /**
   * Resolves an entity reference to its actual value from the domain
   * @param reference - Reference string in format [EntityId.field] or [field]
   * @returns The resolved value from the entity data
   */
  private getReferenceValue(reference: string): any {
    const referenceKeys = this.getReferenceKeys(reference);

    // If the first key exists in the current entity, prepend the current entity ID
    // This allows shorthand references like [field] instead of [EntityId.field]
    if (Cypress.sdt.domain.checkCurrentEntityContainsField(referenceKeys[0])) {
      const currentEntityId = Cypress.sdt.domain.getCurrentEntityId();
      if (currentEntityId) {
        referenceKeys.unshift(currentEntityId);
      }
    }

    // Get all entities from the domain
    const entities = Cypress.sdt.domain.getAllEntities();

    // Navigate through the reference path to get the final value
    const value = referenceKeys.reduce((result, part) => {
      let fieldValue = h.getObjectFieldValueWithNormalizedKey(result, part);

      // If the field value contains references or is an object, parse it recursively
      if (
        (typeof fieldValue === "string" && fieldValue.includes("[")) ||
        typeof fieldValue === "object"
      ) {
        fieldValue = this.parse(fieldValue);
      }

      return fieldValue;
    }, entities);

    return value;
  }

  /**
   * Extracts and normalizes the keys from a reference string
   * @param reference - Reference string like [Entity.field] or [Entity][field]
   * @returns Array of key parts for navigation
   */
  private getReferenceKeys(reference: string): string[] {
    return reference
      .replace(/^\[/g, "") // Remove opening bracket
      .replace(/\]$/g, "") // Remove closing bracket
      .replace(/\] *\[/g, ".") // Convert ][  to .
      .split("."); // Split on dots to get key path
  }
}
