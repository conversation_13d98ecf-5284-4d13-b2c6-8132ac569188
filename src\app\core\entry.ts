import Parser from "./parser";
import h from "@/app/helpers/all";

/**
 * Entry class handles parsing and expansion of entry strings into structured data.
 * It can process single entries or comma-separated lists of entries, with support
 * for entity references, primitives, and special syntax patterns.
 *
 * @example
 * // Single entry
 * const entry = new Entry("abc", false);
 *
 * // List entry with entity references
 * const listEntry = new Entry("[Entity1.Value], abc", true, primitives);
 */
export default class Entry {
  /** The expanded/parsed result of the entry processing */
  public expandedEntry: any = null;

  /** Parser instance for handling entity references and primitives */
  private readonly parser: Parser;

  /**
   * Creates a new Entry instance
   * @param entry - The entry string or object to process
   * @param canBeList - Whether the entry can be parsed as a comma-separated list
   * @param primitives - Optional primitives object for parser functionality
   */
  constructor(
    private readonly entry: any,
    private readonly canBeList: boolean = false,
    primitives: any = null
  ) {
    this.parser = new Parser(primitives);

    // Only process string entries, pass through other types as-is
    if (entry && h.isString(entry)) {
      this.expandedEntry = this.expandEntry();
    } else {
      this.expandedEntry = entry;
    }
  }

  /**
   * Expands the entry string into its parsed form
   * @returns The expanded entry (single value or array based on canBeList)
   */
  private expandEntry(): any {
    if (!this.entry) {
      this.expandedEntry = this.entry;
      return this.expandedEntry;
    }

    try {
      if (!this.canBeList) {
        // Single entry mode - parse as single value
        this.expandedEntry = this.parser.parse(this.entry);
      } else {
        // List mode - parse as comma-separated list
        this.expandedEntry = this.expandEntryItems(this.entry) ?? [];
      }
    } catch (error) {
      console.warn(`Entry expansion failed for: "${this.entry}"`, error);
      // Fallback to original entry on parse error
      this.expandedEntry = this.canBeList ? [this.entry] : this.entry;
    }

    return this.expandedEntry;
  }

  /**
   * Parses a comma-separated entry string into individual elements,
   * respecting bracket nesting to avoid splitting within entity references
   * @param entry - The entry string to parse
   * @returns Array of expanded entry elements, or null if entry is falsy
   */
  private expandEntryItems(entry: string): any[] | null {
    if (!entry) return null;

    const entryElements: string[] = [];
    let current = "";
    let bracketDepth = 0;

    // Parse character by character, tracking bracket depth
    for (let i = 0; i < entry.length; i++) {
      const char = entry[i];

      // Track bracket nesting for entity references like [Entity.Field]
      if (char === "[") bracketDepth++;
      if (char === "]") bracketDepth--;

      // Split on comma only at top level (bracketDepth === 0)
      if (char === "," && bracketDepth === 0) {
        const trimmed = current.trim();
        if (trimmed) entryElements.push(trimmed);
        current = "";

        // Skip whitespace after comma
        while (i + 1 < entry.length && /\s/.test(entry[i + 1])) {
          i++;
        }
        continue;
      }

      current += char;
    }

    // Add the final element if it exists
    const finalTrimmed = current.trim();
    if (finalTrimmed) entryElements.push(finalTrimmed);

    // Expand each element and filter out falsy results
    const expandedEntryElements = entryElements.reduce<any[]>(
      (result, entryElement) => {
        const expandedEntryElement = this.expandItem(entryElement);
        if (
          expandedEntryElement !== null &&
          expandedEntryElement !== undefined
        ) {
          result.push(expandedEntryElement);
        }
        return result;
      },
      []
    );

    return expandedEntryElements;
  }

  /**
   * Expands a single entry element, applying special handling for certain patterns
   * @param entryElement - The entry element string to expand
   * @returns The expanded element (may be string, object, or other type)
   */
  private expandItem(entryElement: string): any {
    if (!entryElement || typeof entryElement !== "string") {
      return entryElement;
    }

    // Skip parsing for special prefixes that should be preserved as-is
    if (entryElement.startsWith("prop-")) return entryElement;
    if (entryElement.startsWith("icon")) return entryElement;

    // Skip parsing for assignment expressions (key=value)
    if (entryElement.includes("=")) return entryElement;

    // Skip parsing for colon expressions where left side starts with bracket
    // This handles cases like "[selector]:nth-of-type(3)"
    if (entryElement.includes(":")) {
      const colonIndex = entryElement.indexOf(":");
      const leftSide = entryElement.slice(0, colonIndex).trim();
      if (leftSide.startsWith("[")) return entryElement;
    }

    // Parse the element through the parser for entity references and primitives
    try {
      const expandedEntryElement = this.parser.parse(entryElement);
      return expandedEntryElement;
    } catch (error) {
      console.warn(`Failed to expand entry element: "${entryElement}"`, error);
      return entryElement; // Return original on parse error
    }
  }
}
